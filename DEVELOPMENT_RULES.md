# 🏗️ Restoran POS - Geliştirm<PERSON>ları

> **Bu dosya projenin kritik geliştirme kurallarını içerir. Sadece sık karşılaşılan sorunları çözen kurallar burada.**

## 📁 **1. KLASÖR YAPISI**

```
atropos/
├── desktop/src/
│   ├── components/  # React bileşenleri (PascalCase)
│   ├── pages/       # Sayfa bileşenleri
│   ├── hooks/       # Custom hooks (camelCase)
│   ├── services/    # API servisleri
│   ├── store/       # Zustand stores
│   └── i18n/        # Dil dosyaları
├── server/src/
│   ├── controllers/ # Route handlers
│   ├── routes/      # API routes
│   ├── services/    # İş mantığı
│   └── validators/  # Validation schemas
├── shared/src/types/ # Ortak tipler
└── prisma/          # Database schema
```

## � **2. İSİMLENDİRME**

- **Dosyalar**: `PascalCase.tsx`, `camelCase.ts`
- **<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>**: `camelCase`
- **Sabitler**: `UPPER_SNAKE_CASE`
- **Database**: Tablolar `PascalCase`, alanlar `camelCase`

## 🌐 **3. API RESPONSE FORMAT**

```typescript
interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}
```

## � **4. i18n KULLANIMI**

```typescript
// Hiyerarşik yapı
const { t } = useTranslation()
<Button>{t('common.save')}</Button>
<Typography>{t('orders.newOrder')}</Typography>
```

## 🚨 **5. KRİTİK VALIDATION KURALLARI**

### ✅ **Query Validation - BUGÜN YAŞANAN SORUN!**
```typescript
// ❌ YANLIŞ - PrismaClientValidationError'a neden olur!
const query = req.query as ProductQueryInput // sortBy undefined olabilir!

// ✅ DOĞRU - validateQuery middleware kullan
export const validateQuery = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Parse ve default değerlerle validate et
      const validatedQuery = schema.parse(req.query)
      ;(req as any).validatedQuery = validatedQuery
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Query validation failed',
          details: error.issues
        })
      }
    }
  }
}

// Controller'da kullanım
const query = (req as any).validatedQuery as ProductQueryInput
```

### ✅ **Body Validation:**
```typescript
export const validateBody = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.body)
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Body validation failed',
          details: error.issues
        })
      }
    }
  }
}
```

## 🔗 **6. PRISMA INCLUDE RELATIONS**

### ✅ **Include Pattern:**
```typescript
// ✅ Doğru include kullanımı
const products = await prisma.product.findMany({
  include: {
    category: true,
    tax: true,
    variants: {
      where: { deletedAt: null },
      orderBy: { displayOrder: 'asc' }
    }
  },
  orderBy: { [query.sortBy]: query.sortOrder }, // query.sortBy garantili
  skip: (query.page - 1) * query.limit,
  take: query.limit
})
```

### 🚨 **CRITICAL: Schema değişikliklerinden sonra:**
```bash
npx prisma generate  # MUTLAKA çalıştır!
```

## 🚨 **7. ERROR HANDLING**

```typescript
// ✅ AppError Class
class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number,
    public code: string
  ) {
    super(message)
  }
}

// ✅ Service'de kullanım
try {
  const result = await someOperation()
  return result
} catch (error) {
  logger.error('Operation failed:', error)
  throw new AppError('Operation failed', 500, 'OPERATION_FAILED')
}
```

## 🔐 **8. AUTHENTICATION**

```typescript
// ✅ Auth middleware
export const requireAuth = (roles?: UserRole[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const token = req.headers.authorization?.split(' ')[1]
      const user = await verifyToken(token)

      if (roles && !roles.includes(user.role)) {
        throw new AppError('Insufficient permissions', 403, 'FORBIDDEN')
      }

      req.user = user
      next()
    } catch (error) {
      next(error)
    }
  }
}
```

## 🗄️ **9. DATABASE TRANSACTIONS**

```typescript
// ✅ Prisma transaction pattern
const createOrderWithItems = async (orderData: CreateOrderInput) => {
  return await prisma.$transaction(async (tx) => {
    const order = await tx.order.create({ data: orderData })

    await tx.orderItem.createMany({
      data: orderData.items.map(item => ({
        ...item,
        orderId: order.id
      }))
    })

    return order
  })
}
```

## 📝 **10. LOGGING**

```typescript
// ✅ Winston logger
import winston from 'winston'

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.Console()
  ]
})

// Kullanım
logger.info('User logged in', { userId: user.id })
logger.error('Order creation failed', { error, orderData })
```

---

## 🎯 **ÖZET - EN ÖNEMLİ KURALLAR**

### 🚨 **1. Query Validation Sorunu (Bugün yaşandı):**
```typescript
// ❌ YANLIŞ: req.query direkt kullanma
const query = req.query as ProductQueryInput

// ✅ DOĞRU: validateQuery middleware kullan
const query = (req as any).validatedQuery as ProductQueryInput
```

### 🚨 **2. Prisma Generate:**
```bash
# Schema değişikliklerinden sonra MUTLAKA:
npx prisma generate
```

### 🚨 **3. Error Handling:**
```typescript
throw new AppError('Message', statusCode, 'ERROR_CODE')
```

### 🚨 **4. API Response:**
```typescript
{ success: boolean, data?: T, message?: string, error?: string }
```

**Bu 4 kural %90 sorunları çözer! 🎯**



